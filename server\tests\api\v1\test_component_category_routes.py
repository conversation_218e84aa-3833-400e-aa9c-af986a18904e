#!/usr/bin/env python3
"""Tests for Component Category functionality.

This module provides comprehensive tests for component category management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentCategory model validation and business logic
- ComponentCategoryRepository data access operations
- ComponentCategoryService business logic and validation
- Component Category API endpoints and error handling
- Hierarchical operations and tree management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_category_repository import ComponentCategoryRepository
from src.core.services.general.component_category_service import ComponentCategoryService
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategorySearchSchema,
)
from src.core.errors.exceptions import BusinessLogic<PERSON>rror, NotFoundError, ValidationError
from src.core.utils.pagination_utils import PaginationParams

class TestComponentCategoryAPI:
    """Test Component Category API endpoints."""

    def test_create_category_endpoint(self, authenticated_client: TestClient):
        """Test POST /component-categories/ endpoint."""
        category_data = {
            "name": "API Test Category",
            "description": "API test description",
            "is_active": True,
        }
        
        response = authenticated_client.post(
            "/api/v1/component-categories/",
            json=category_data,
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "API Test Category"
        assert data["description"] == "API test description"
        assert data["is_active"] is True

    def test_get_category_endpoint(self, authenticated_client: TestClient):
        """Test GET /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Get Test Category",
            "description": "Get test description",
            "is_active": True,
        }

        create_response = authenticated_client.post(
            "/api/v1/component-categories/",
            json=category_data,
        )
        category_id = create_response.json()["id"]

        # Then retrieve it
        response = authenticated_client.get(
            f"/api/v1/component-categories/{category_id}",
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == category_id
        assert data["name"] == "Get Test Category"

    def test_list_categories_endpoint(self, authenticated_client: TestClient):
        """Test GET /component-categories/ endpoint."""
        response = authenticated_client.get(
            "/api/v1/component-categories/",
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "categories" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data

    def test_update_category_endpoint(self, authenticated_client: TestClient):
        """Test PUT /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Update Test Category",
            "description": "Update test description",
            "is_active": True,
        }

        create_response = authenticated_client.post(
            "/api/v1/component-categories/",
            json=category_data,
        )
        category_id = create_response.json()["id"]

        # Then update it
        update_data = {
            "name": "Updated Category Name",
            "description": "Updated description",
        }

        response = authenticated_client.put(
            f"/api/v1/component-categories/{category_id}",
            json=update_data,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Category Name"
        assert data["description"] == "Updated description"

    def test_delete_category_endpoint(self, authenticated_client: TestClient):
        """Test DELETE /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Delete Test Category",
            "description": "Delete test description",
            "is_active": True,
        }

        create_response = authenticated_client.post(
            "/api/v1/component-categories/",
            json=category_data,
        )
        category_id = create_response.json()["id"]

        # Then delete it
        response = authenticated_client.delete(
            f"/api/v1/component-categories/{category_id}",
        )

        assert response.status_code == 204

        # Verify it's deleted
        get_response = authenticated_client.get(
            f"/api/v1/component-categories/{category_id}",
        )
        assert get_response.status_code == 404

    def test_get_category_tree_endpoint(self, authenticated_client: TestClient):
        """Test GET /component-categories/tree endpoint."""
        response = authenticated_client.get(
            "/api/v1/component-categories/tree",
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "tree" in data
        assert "total_categories" in data
        assert "max_depth" in data
