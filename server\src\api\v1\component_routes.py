#!/usr/bin/env python3
"""Component API Routes for Ultimate Electrical Designer.

This module provides REST API endpoints for component management operations,
including CRUD operations, search, filtering, and bulk operations for
electrical component catalog management.

Key Features:
- Complete CRUD operations with comprehensive validation
- Advanced search and filtering with pagination
- Bulk operations support with transaction management
- Professional error handling and logging
- OpenAPI documentation with detailed examples
- Authentication and authorization integration
- Performance monitoring and caching
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse

from src.config.logging_config import logger
from src.core.enums.electrical_enums import ComponentCategoryType, ComponentType
from src.core.errors.exceptions import (
    BusinessLogicError,
    InvalidInputError,
    NotFoundError,
    ValidationError,
)
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.schemas.base import PaginationSchema
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.component_schemas import (
    ComponentAdvancedSearchResponseSchema,
    ComponentAdvancedSearchSchema,
    ComponentBulkCreateSchema,
    ComponentBulkUpdateSchema,
    ComponentCreateSchema,
    ComponentPaginatedResponseSchema,
    ComponentReadSchema,
    ComponentSearchResultSchema,
    ComponentSearchSchema,
    ComponentStatsSchema,
    ComponentSummarySchema,
    ComponentUpdateSchema,
    ComponentValidationResultSchema,
)
from src.core.security.enhanced_dependencies import require_authenticated_user
from src.core.services.dependencies import get_component_service
from src.core.services.general.component_service import ComponentService
from src.core.utils.pagination_utils import PaginationParams

# Create router instance
router = APIRouter(prefix="/components")


# ============================================================================
# COMPONENT CRUD ENDPOINTS
# ============================================================================

@router.post(
    "/",
    response_model=ComponentReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create Component",
    description="Create a new electrical component with comprehensive validation",
    responses={
        status.HTTP_201_CREATED: {
            "description": "Component created successfully",
            "model": ComponentReadSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid component data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "Component already exists",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("create_component")
@monitor_api_performance("create_component")
async def create_component(
    component_data: ComponentCreateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentReadSchema:
    """Create a new electrical component.

    This endpoint creates a new component in the electrical catalog with
    comprehensive validation and business rule enforcement.

    Args:
        component_data: Component creation data
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        ComponentReadSchema: Created component data

    Raises:
        HTTPException: If validation fails or component already exists
    """
    logger.info(f"Creating component: {component_data.manufacturer} {component_data.model_number}")
    logger.debug(f"Component creation requested by user: {current_user.get('id')}")

    try:
        created_component = component_service.create_component(component_data)
        logger.info(f"Component created successfully: {created_component.id}")
        return created_component

    except ValidationError as e:
        logger.warning(f"Component validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Component validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Component creation business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )


# ============================================================================
# COMPONENT CATEGORY AND TYPE ENDPOINTS (moved before parameterized routes)
# ============================================================================

@router.get(
    "/categories",
    response_model=List[Dict[str, str]],
    status_code=status.HTTP_200_OK,
    summary="List Component Categories",
    description="Get list of available component categories",
    responses={
        status.HTTP_200_OK: {
            "description": "Component categories retrieved successfully",
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("list_component_categories")
@monitor_api_performance("list_component_categories")
async def list_component_categories(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
) -> List[Dict[str, str]]:
    """Get list of available component categories.

    Args:
        current_user: Current authenticated user

    Returns:
        List[Dict[str, str]]: List of categories with value and description
    """
    logger.debug("Retrieving component categories")

    categories = [
        {"value": category.value, "description": category.value.replace("_", " ").title()}
        for category in ComponentCategoryType
    ]

    logger.debug(f"Retrieved {len(categories)} component categories")
    return categories


@router.get(
    "/types",
    response_model=List[Dict[str, str]],
    status_code=status.HTTP_200_OK,
    summary="List Component Types",
    description="Get list of available component types, optionally filtered by category",
    responses={
        status.HTTP_200_OK: {
            "description": "Component types retrieved successfully",
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("list_component_types")
@monitor_api_performance("list_component_types")
async def list_component_types(
    category: Optional[ComponentCategoryType] = Query(None, description="Filter by category"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
) -> List[Dict[str, str]]:
    """Get list of available component types.

    Args:
        category: Optional category filter
        current_user: Current authenticated user

    Returns:
        List[Dict[str, str]]: List of component types with value and description
    """
    logger.debug(f"Retrieving component types for category: {category}")

    if category:
        # Filter types by category using the mapping
        from src.core.enums.electrical_enums import COMPONENT_TYPE_TO_CATEGORY_MAPPING
        filtered_types = [
            comp_type for comp_type, cat in COMPONENT_TYPE_TO_CATEGORY_MAPPING.items()
            if cat == category
        ]
    else:
        filtered_types = list(ComponentType)

    types = [
        {"value": comp_type.value, "description": comp_type.value.replace("_", " ").title()}
        for comp_type in filtered_types
    ]

    logger.debug(f"Retrieved {len(types)} component types")
    return types


@router.get(
    "/preferred",
    response_model=List[ComponentSummarySchema],
    status_code=status.HTTP_200_OK,
    summary="Get Preferred Components",
    description="Get components marked as preferred",
    responses={
        status.HTTP_200_OK: {
            "description": "Preferred components retrieved successfully",
            "model": List[ComponentSummarySchema],
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_preferred_components")
@monitor_api_performance("get_preferred_components")
async def get_preferred_components(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> List[ComponentSummarySchema]:
    """Get preferred components.

    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        List[ComponentSummarySchema]: List of preferred components
    """
    logger.debug("Retrieving preferred components")

    components = component_service.get_preferred_components(skip, limit)

    logger.debug(f"Retrieved {len(components)} preferred components")
    return components


@router.get(
    "/stats",
    response_model=ComponentStatsSchema,
    status_code=status.HTTP_200_OK,
    summary="Get Component Statistics",
    description="Get component catalog statistics and analytics",
    responses={
        status.HTTP_200_OK: {
            "description": "Component statistics retrieved successfully",
            "model": ComponentStatsSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_component_stats")
@monitor_api_performance("get_component_stats")
async def get_component_stats(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentStatsSchema:
    """Get component statistics and analytics.

    Args:
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        ComponentStatsSchema: Component statistics
    """
    logger.debug("Retrieving component statistics")

    stats = component_service.get_component_stats()

    logger.debug(f"Component statistics retrieved: {stats.total_components} total components")
    return stats


@router.get(
    "/{component_id}",
    response_model=ComponentReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Get Component by ID",
    description="Retrieve a specific component by its unique identifier",
    responses={
        status.HTTP_200_OK: {
            "description": "Component data retrieved successfully",
            "model": ComponentReadSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Component not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_component")
@monitor_api_performance("get_component")
async def get_component(
    component_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentReadSchema:
    """Get component by ID.

    Args:
        component_id: Unique component identifier
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        ComponentReadSchema: Component data

    Raises:
        HTTPException: If component not found
    """
    logger.debug(f"Retrieving component: {component_id}")

    try:
        component = component_service.get_component(component_id)
        logger.debug(f"Component retrieved successfully: {component_id}")
        return component

    except NotFoundError as e:
        logger.warning(f"Component not found: {component_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.put(
    "/{component_id}",
    response_model=ComponentReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Update Component",
    description="Update an existing component with validation",
    responses={
        status.HTTP_200_OK: {
            "description": "Component updated successfully",
            "model": ComponentReadSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid update data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Component not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "Update conflicts with existing data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("update_component")
@monitor_api_performance("update_component")
async def update_component(
    component_id: int,
    update_data: ComponentUpdateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentReadSchema:
    """Update an existing component.

    Args:
        component_id: Component ID to update
        update_data: Component update data
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        ComponentReadSchema: Updated component data

    Raises:
        HTTPException: If component not found or validation fails
    """
    logger.info(f"Updating component: {component_id}")
    logger.debug(f"Component update requested by user: {current_user.get('id')}")

    try:
        updated_component = component_service.update_component(component_id, update_data)
        logger.info(f"Component updated successfully: {component_id}")
        return updated_component

    except NotFoundError as e:
        logger.warning(f"Component not found for update: {component_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        logger.warning(f"Component update validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Component update validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Component update business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )


@router.delete(
    "/{component_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Component",
    description="Soft delete a component (marks as deleted but preserves data)",
    responses={
        status.HTTP_204_NO_CONTENT: {
            "description": "Component deleted successfully"
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Component not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "Component has dependencies and cannot be deleted",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("delete_component")
@monitor_api_performance("delete_component")
async def delete_component(
    component_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> JSONResponse:
    """Delete a component (soft delete).

    Args:
        component_id: Component ID to delete
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        JSONResponse: Empty response with 204 status

    Raises:
        HTTPException: If component not found or has dependencies
    """
    logger.info(f"Deleting component: {component_id}")
    logger.debug(f"Component deletion requested by user: {current_user.get('id')}")

    try:
        success = component_service.delete_component(
            component_id, 
            deleted_by_user_id=current_user.get('id')
        )
        
        if success:
            logger.info(f"Component deleted successfully: {component_id}")
            return JSONResponse(
                status_code=status.HTTP_204_NO_CONTENT,
                content=None
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete component"
            )

    except NotFoundError as e:
        logger.warning(f"Component not found for deletion: {component_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except BusinessLogicError as e:
        logger.warning(f"Component deletion business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )


# ============================================================================
# COMPONENT SEARCH AND FILTERING ENDPOINTS
# ============================================================================

@router.get(
    "/",
    response_model=ComponentPaginatedResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="List Components",
    description="List components with pagination, search, and filtering",
    responses={
        status.HTTP_200_OK: {
            "description": "Components retrieved successfully",
            "model": ComponentPaginatedResponseSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid search parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("list_components")
@monitor_api_performance("list_components")
async def list_components(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search_term: Optional[str] = Query(None, description="Search term"),
    category: Optional[ComponentCategoryType] = Query(None, description="Filter by category"),
    component_type: Optional[ComponentType] = Query(None, description="Filter by type"),
    manufacturer: Optional[str] = Query(None, description="Filter by manufacturer"),
    is_preferred: Optional[bool] = Query(None, description="Filter by preferred status"),
    is_active: Optional[bool] = Query(True, description="Filter by active status"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentPaginatedResponseSchema:
    """List components with pagination and filtering.

    Args:
        page: Page number (1-based)
        size: Number of items per page
        search_term: Search term for name, description, manufacturer
        category: Filter by component category
        component_type: Filter by component type
        manufacturer: Filter by manufacturer
        is_preferred: Filter by preferred status
        is_active: Filter by active status
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        ComponentPaginatedResponseSchema: Paginated component list

    Raises:
        HTTPException: If search parameters are invalid
    """
    logger.debug(f"Listing components - page: {page}, size: {size}")

    try:
        # Build search parameters
        search_params = ComponentSearchSchema(
            search_term=search_term,
            category=category,
            component_type=component_type,
            manufacturer=manufacturer,
            is_preferred=is_preferred,
            is_active=is_active,
            min_price=None,
            max_price=None,
            currency=None,
            stock_status=None,
            specifications=None,
        )

        # Build pagination parameters
        pagination_params = PaginationParams(
            page=page,
            per_page=size,
        )

        # Perform search
        result = component_service.search_components(search_params, pagination_params)
        
        logger.debug(f"Components retrieved: {len(result.items)} items, total: {result.pagination.total}")
        return result

    except ValidationError as e:
        logger.warning(f"Component search validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Search parameters validation failed: {str(e)}"
        )


@router.get(
    "/search",
    response_model=ComponentPaginatedResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Advanced Component Search",
    description="Advanced search with specification-based filtering",
    responses={
        status.HTTP_200_OK: {
            "description": "Search results retrieved successfully",
            "model": ComponentPaginatedResponseSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid search parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("advanced_search_components")
@monitor_api_performance("advanced_search_components")
async def advanced_search_components(
    search_params: ComponentSearchSchema,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentPaginatedResponseSchema:
    """Advanced component search with specification filtering.

    Args:
        search_params: Advanced search parameters
        page: Page number (1-based)
        size: Number of items per page
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        ComponentPaginatedResponseSchema: Paginated search results

    Raises:
        HTTPException: If search parameters are invalid
    """
    logger.debug(f"Advanced component search requested")

    try:
        # Build pagination parameters
        pagination_params = PaginationParams(
            page=page,
            per_page=size,
        )

        # Perform advanced search
        result = component_service.search_components(search_params, pagination_params)
        
        logger.debug(f"Advanced search completed: {len(result.items)} items found")
        return result

    except ValidationError as e:
        logger.warning(f"Advanced search validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Search parameters validation failed: {str(e)}"
        )


@router.post(
    "/search/specifications",
    response_model=ComponentPaginatedResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Search Components by Specifications",
    description="Enhanced specification-based search with technical filtering",
    responses={
        status.HTTP_200_OK: {
            "description": "Specification search results retrieved successfully",
            "model": ComponentPaginatedResponseSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid specification parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("search_components_by_specifications")
@monitor_api_performance("search_components_by_specifications")
async def search_components_by_specifications(
    specifications: Dict[str, Any],
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentPaginatedResponseSchema:
    """Search components by technical specifications.
    
    This endpoint provides enhanced specification-based search capabilities,
    allowing users to filter components by technical specifications like
    electrical ratings, physical dimensions, and compliance standards.
    
    Args:
        specifications: Dictionary of specification criteria
        page: Page number (1-based)
        size: Number of items per page
        current_user: Current authenticated user
        component_service: Component service dependency
    
    Returns:
        ComponentPaginatedResponseSchema: Paginated specification search results
    
    Raises:
        HTTPException: If specification parameters are invalid
    """
    logger.debug(f"Specification-based search requested: {specifications}")
    
    try:
        # Build pagination parameters
        pagination_params = PaginationParams(
            page=page,
            per_page=size,
        )
        
        # Perform specification-based search using advanced method
        result = component_service.search_components_advanced(
            search_filters={},
            specification_filters=specifications,
            price_range=None,
            pagination_params=pagination_params,
        )
        
        logger.debug(f"Specification search completed: {len(result.items)} items found")
        return result
        
    except ValidationError as e:
        logger.warning(f"Specification search validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Specification parameters validation failed: {str(e)}"
        )


@router.get(
    "/search/suggestions",
    response_model=List[str],
    status_code=status.HTTP_200_OK,
    summary="Get Search Suggestions",
    description="Get search suggestions for autocomplete functionality",
    responses={
        status.HTTP_200_OK: {
            "description": "Search suggestions retrieved successfully",
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid search parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_search_suggestions")
@monitor_api_performance("get_search_suggestions")
async def get_search_suggestions(
    query: str = Query(..., min_length=2, description="Search query for suggestions"),
    field: str = Query("name", description="Field to search for suggestions (name, manufacturer, part_number)"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of suggestions"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> List[str]:
    """Get search suggestions for autocomplete.
    
    This endpoint provides search suggestions based on component data,
    supporting autocomplete functionality for names, manufacturers, and part numbers.
    
    Args:
        query: Search query string
        field: Field to search (name, manufacturer, part_number)
        limit: Maximum number of suggestions
        current_user: Current authenticated user
        component_service: Component service dependency
    
    Returns:
        List[str]: List of search suggestions
    
    Raises:
        HTTPException: If search parameters are invalid
    """
    logger.debug(f"Search suggestions requested for query: {query}, field: {field}")
    
    try:
        # Validate field parameter
        valid_fields = ["name", "manufacturer", "part_number"]
        if field not in valid_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid field '{field}'. Must be one of: {valid_fields}"
            )
        
        # Build search filters for suggestions
        search_filters = {
            "search_term": query,
            "is_active": True,
        }
        
        # Perform search to get components
        result = component_service.search_components_advanced(
            search_filters=search_filters,
            specification_filters=None,
            price_range=None,
            pagination_params=PaginationParams(page=1, per_page=limit * 2),  # Get more to filter duplicates
        )
        
        # Extract unique suggestions based on field
        suggestions = set()
        for component in result.items:
            if field == "name" and component.name:
                suggestions.add(component.name)
            elif field == "manufacturer" and component.manufacturer:
                suggestions.add(component.manufacturer)
            elif field == "part_number" and component.part_number:
                suggestions.add(component.part_number)
        
        # Convert to sorted list and limit results
        suggestion_list = sorted(list(suggestions))[:limit]
        
        logger.debug(f"Search suggestions completed: {len(suggestion_list)} suggestions")
        return suggestion_list

    except ValidationError as e:
        logger.warning(f"Search suggestions validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Search parameters validation failed: {str(e)}"
        )


# ============================================================================
# ADVANCED SEARCH ENDPOINTS
# ============================================================================

@router.post(
    "/search/advanced",
    response_model=ComponentAdvancedSearchResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Advanced Component Search",
    description="Perform advanced component search with complex filtering and relevance scoring",
    responses={
        status.HTTP_200_OK: {
            "description": "Advanced search completed successfully",
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid search parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("advanced_component_search")
@monitor_api_performance("advanced_component_search")
async def advanced_component_search(
    search_params: ComponentAdvancedSearchSchema,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentAdvancedSearchResponseSchema:
    """Perform advanced component search with complex filtering.

    This endpoint provides sophisticated search capabilities including:
    - Complex specification filtering with range queries
    - Boolean logic operators (AND, OR, NOT)
    - Fuzzy text matching
    - Relevance scoring
    - Multiple filter types (basic, specification, range)

    Args:
        search_params: Advanced search parameters
        page: Page number (1-based)
        size: Number of items per page
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        ComponentAdvancedSearchResponseSchema: Advanced search results with metadata

    Raises:
        HTTPException: If search parameters are invalid
    """
    logger.debug(f"Advanced component search requested with {len(search_params.model_dump())} parameters")

    try:
        # Build pagination parameters
        pagination_params = PaginationParams(
            page=page,
            per_page=size,
        )

        # Perform advanced search
        result = component_service.search_components_with_builder(
            search_params, pagination_params
        )

        logger.debug(f"Advanced search completed: {len(result.items)} items found")
        return result

    except ValidationError as e:
        logger.warning(f"Advanced search validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Search parameters validation failed: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error in advanced search: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Advanced search failed"
        )


@router.get(
    "/search/relevance",
    response_model=ComponentAdvancedSearchResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Relevance-Based Component Search",
    description="Search components with relevance scoring and ranking",
    responses={
        status.HTTP_200_OK: {
            "description": "Relevance search completed successfully",
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid search parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("relevance_component_search")
@monitor_api_performance("relevance_component_search")
async def relevance_component_search(
    search_term: str = Query(..., min_length=2, description="Search term"),
    search_fields: Optional[str] = Query(None, description="Comma-separated list of fields to search"),
    fuzzy: bool = Query(False, description="Enable fuzzy matching"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> ComponentAdvancedSearchResponseSchema:
    """Search components with relevance scoring.

    This endpoint provides relevance-based search with:
    - Automatic relevance scoring based on match quality
    - Field-specific matching weights
    - Fuzzy matching support
    - Results ranked by relevance

    Args:
        search_term: Text to search for
        search_fields: Comma-separated fields to search (name,description,manufacturer,part_number)
        fuzzy: Whether to enable fuzzy matching
        page: Page number (1-based)
        size: Number of items per page
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        ComponentAdvancedSearchResponseSchema: Search results with relevance scores

    Raises:
        HTTPException: If search parameters are invalid
    """
    logger.debug(f"Relevance search requested for: {search_term}")

    try:
        # Parse search fields
        fields_list = None
        if search_fields:
            fields_list = [field.strip() for field in search_fields.split(",")]
            # Validate fields
            valid_fields = ["name", "description", "manufacturer", "part_number"]
            invalid_fields = [f for f in fields_list if f not in valid_fields]
            if invalid_fields:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid search fields: {invalid_fields}. Valid fields: {valid_fields}"
                )

        # Build pagination parameters
        pagination_params = PaginationParams(
            page=page,
            per_page=size,
        )

        # Perform relevance search
        result = component_service.search_components_with_relevance(
            search_term=search_term,
            search_fields=fields_list,
            fuzzy=fuzzy,
            pagination_params=pagination_params
        )

        logger.debug(f"Relevance search completed: {len(result.items)} items found")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in relevance search: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Relevance search failed"
        )


# ============================================================================
# COMPONENT CATEGORY AND TYPE ENDPOINTS
# ============================================================================


@router.get(
    "/manufacturers",
    response_model=List[str],
    status_code=status.HTTP_200_OK,
    summary="List Component Manufacturers",
    description="Get list of manufacturers from existing components",
    responses={
        status.HTTP_200_OK: {
            "description": "Manufacturers retrieved successfully",
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("list_component_manufacturers")
@monitor_api_performance("list_component_manufacturers")
async def list_component_manufacturers(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> List[str]:
    """Get list of manufacturers from existing components.

    Args:
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        List[str]: List of unique manufacturers
    """
    logger.debug("Retrieving component manufacturers")

    # Use the repository method to get manufacturers
    manufacturers = component_service.component_repo.get_manufacturers_list()

    logger.debug(f"Retrieved {len(manufacturers)} manufacturers")
    return manufacturers


# ============================================================================
# COMPONENT FILTERING BY CATEGORY/TYPE ENDPOINTS
# ============================================================================

@router.get(
    "/by-category/{category}",
    response_model=List[ComponentSummarySchema],
    status_code=status.HTTP_200_OK,
    summary="Get Components by Category",
    description="Get components filtered by category",
    responses={
        status.HTTP_200_OK: {
            "description": "Components retrieved successfully",
            "model": List[ComponentSummarySchema],
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_components_by_category")
@monitor_api_performance("get_components_by_category")
async def get_components_by_category(
    category: ComponentCategoryType,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> List[ComponentSummarySchema]:
    """Get components by category.

    Args:
        category: Component category
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        List[ComponentSummarySchema]: List of components in category
    """
    logger.debug(f"Retrieving components by category: {category}")

    components = component_service.get_components_by_category(category, skip, limit)
    
    logger.debug(f"Retrieved {len(components)} components for category: {category}")
    return components


@router.get(
    "/by-type/{component_type}",
    response_model=List[ComponentSummarySchema],
    status_code=status.HTTP_200_OK,
    summary="Get Components by Type",
    description="Get components filtered by type",
    responses={
        status.HTTP_200_OK: {
            "description": "Components retrieved successfully",
            "model": List[ComponentSummarySchema],
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_components_by_type")
@monitor_api_performance("get_components_by_type")
async def get_components_by_type(
    component_type: ComponentType,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> List[ComponentSummarySchema]:
    """Get components by type.

    Args:
        component_type: Component type
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        List[ComponentSummarySchema]: List of components of specified type
    """
    logger.debug(f"Retrieving components by type: {component_type}")

    components = component_service.get_components_by_type(component_type, skip, limit)
    
    logger.debug(f"Retrieved {len(components)} components for type: {component_type}")
    return components


# ============================================================================
# BULK OPERATIONS ENDPOINTS
# ============================================================================

@router.post(
    "/bulk/create",
    response_model=List[ComponentValidationResultSchema],
    status_code=status.HTTP_201_CREATED,
    summary="Bulk Create Components",
    description="Create multiple components in a single transaction with validation",
    responses={
        status.HTTP_201_CREATED: {
            "description": "Components created successfully",
            "model": List[ComponentValidationResultSchema],
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid bulk create data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("bulk_create_components")
@monitor_api_performance("bulk_create_components")
async def bulk_create_components(
    bulk_data: ComponentBulkCreateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> List[ComponentValidationResultSchema]:
    """Bulk create components with comprehensive validation.
    
    This endpoint allows creating multiple components in a single transaction,
    with individual validation results for each component.
    
    Args:
        bulk_data: Bulk creation data containing list of components
        current_user: Current authenticated user
        component_service: Component service dependency
    
    Returns:
        List[ComponentValidationResultSchema]: Creation results for each component
    
    Raises:
        HTTPException: If bulk validation fails
    """
    logger.info(f"Bulk create requested for {len(bulk_data.components)} components")
    logger.debug(f"Bulk create requested by user: {current_user.get('id')}")
    
    try:
        # Perform bulk creation
        results = component_service.bulk_create_components(bulk_data.components)
        
        # Count successful and failed operations
        successful = sum(1 for r in results if r.is_valid)
        failed = len(results) - successful
        
        logger.info(f"Bulk create completed: {successful} successful, {failed} failed")
        return results
        
    except ValidationError as e:
        logger.warning(f"Bulk create validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bulk creation validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Bulk create business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post(
    "/bulk/update",
    response_model=List[ComponentValidationResultSchema],
    status_code=status.HTTP_200_OK,
    summary="Bulk Update Components",
    description="Update multiple components in a single transaction with validation",
    responses={
        status.HTTP_200_OK: {
            "description": "Components updated successfully",
            "model": List[ComponentValidationResultSchema],
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid bulk update data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("bulk_update_components")
@monitor_api_performance("bulk_update_components")
async def bulk_update_components(
    bulk_data: ComponentBulkUpdateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> List[ComponentValidationResultSchema]:
    """Bulk update components with comprehensive validation.
    
    This endpoint allows updating multiple components in a single transaction,
    with individual validation results for each component.
    
    Args:
        bulk_data: Bulk update data containing list of component updates
        current_user: Current authenticated user
        component_service: Component service dependency
    
    Returns:
        List[ComponentValidationResultSchema]: Update results for each component
    
    Raises:
        HTTPException: If bulk validation fails
    """
    logger.info(f"Bulk update requested for {len(bulk_data.component_ids)} components")
    logger.debug(f"Bulk update requested by user: {current_user.get('id')}")
    
    try:
        # Perform bulk update
        results = component_service.bulk_update_components(bulk_data.component_ids, bulk_data.update_data)
        
        # Count successful and failed operations
        successful = sum(1 for r in results if r.is_valid)
        failed = len(results) - successful
        
        logger.info(f"Bulk update completed: {successful} successful, {failed} failed")
        return results
        
    except ValidationError as e:
        logger.warning(f"Bulk update validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bulk update validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Bulk update business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post(
    "/export",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Export Components",
    description="Export components to various formats (JSON, CSV, XLSX)",
    responses={
        status.HTTP_200_OK: {
            "description": "Components exported successfully",
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid export parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("export_components")
@monitor_api_performance("export_components")
async def export_components(
    format: str = Query("json", description="Export format (json, csv, xlsx)"),
    component_ids: Optional[List[int]] = Query(None, description="Specific component IDs to export"),
    search_filters: Optional[Dict[str, Any]] = None,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> Dict[str, Any]:
    """Export components to various formats.
    
    This endpoint allows exporting components to JSON, CSV, or XLSX formats
    with optional filtering and selection of specific components.
    
    Args:
        format: Export format (json, csv, xlsx)
        component_ids: Optional list of specific component IDs to export
        search_filters: Optional search filters for component selection
        current_user: Current authenticated user
        component_service: Component service dependency
    
    Returns:
        Dict[str, Any]: Export result with data and metadata
    
    Raises:
        HTTPException: If export parameters are invalid
    """
    logger.info(f"Component export requested in format: {format}")
    logger.debug(f"Export requested by user: {current_user.get('id')}")
    
    # Validate export format
    valid_formats = ["json", "csv", "xlsx"]
    if format not in valid_formats:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid export format '{format}'. Must be one of: {valid_formats}"
        )
    
    try:
        # Perform export
        export_result = component_service.export_components(
            format=format,
            component_ids=component_ids,
            search_filters=search_filters or {},
        )
        
        logger.info(f"Export completed: {export_result.get('count', 0)} components exported")
        return export_result
        
    except ValidationError as e:
        logger.warning(f"Export validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Export validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Export business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post(
    "/import",
    response_model=Dict[str, Any],
    status_code=status.HTTP_201_CREATED,
    summary="Import Components",
    description="Import components from various formats (JSON, CSV, XLSX)",
    responses={
        status.HTTP_201_CREATED: {
            "description": "Components imported successfully",
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid import data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("import_components")
@monitor_api_performance("import_components")
async def import_components(
    import_data: Dict[str, Any],
    format: str = Query("json", description="Import format (json, csv, xlsx)"),
    validate_only: bool = Query(False, description="Only validate without importing"),
    update_existing: bool = Query(False, description="Update existing components"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> Dict[str, Any]:
    """Import components from various formats.
    
    This endpoint allows importing components from JSON, CSV, or XLSX formats
    with validation, duplicate handling, and optional update of existing components.
    
    Args:
        import_data: Import data containing component information
        format: Import format (json, csv, xlsx)
        validate_only: Only validate without importing
        update_existing: Update existing components instead of creating duplicates
        current_user: Current authenticated user
        component_service: Component service dependency
    
    Returns:
        Dict[str, Any]: Import result with validation and creation results
    
    Raises:
        HTTPException: If import data is invalid
    """
    logger.info(f"Component import requested in format: {format}")
    logger.debug(f"Import requested by user: {current_user.get('id')}")
    
    # Validate import format
    valid_formats = ["json", "csv", "xlsx"]
    if format not in valid_formats:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid import format '{format}'. Must be one of: {valid_formats}"
        )
    
    try:
        # Perform import
        import_result = component_service.import_components(
            import_data=import_data,
            format=format,
            validate_only=validate_only,
            update_existing=update_existing,
        )
        
        logger.info(f"Import completed: {import_result.get('processed', 0)} components processed")
        return import_result
        
    except ValidationError as e:
        logger.warning(f"Import validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Import validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Import business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


# ============================================================================
# ENHANCED BULK OPERATIONS ENDPOINTS
# ============================================================================

@router.post(
    "/bulk/create-validated",
    response_model=Dict[str, Any],
    status_code=status.HTTP_201_CREATED,
    summary="Enhanced Bulk Create Components",
    description="Create multiple components with comprehensive validation and duplicate checking",
    responses={
        status.HTTP_201_CREATED: {
            "description": "Bulk creation completed with results",
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid component data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("enhanced_bulk_create_components")
@monitor_api_performance("enhanced_bulk_create_components")
async def enhanced_bulk_create_components(
    components_data: List[Dict[str, Any]],
    validate_duplicates: bool = Query(True, description="Check for duplicate components"),
    batch_size: int = Query(100, ge=1, le=500, description="Batch size for processing"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> Dict[str, Any]:
    """Enhanced bulk create components with comprehensive validation.

    This endpoint provides advanced bulk creation with:
    - Comprehensive validation and error reporting
    - Duplicate detection and handling
    - Batch processing for performance
    - Detailed result statistics

    Args:
        components_data: List of component data dictionaries
        validate_duplicates: Whether to check for duplicate components
        batch_size: Number of components to process in each batch
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        Dict[str, Any]: Creation results with statistics and errors

    Raises:
        HTTPException: If bulk creation fails
    """
    logger.info(f"Enhanced bulk create requested for {len(components_data)} components")

    try:
        # Perform enhanced bulk creation
        result = component_service.bulk_create_with_validation(
            components_data=components_data,
            validate_duplicates=validate_duplicates,
            batch_size=batch_size
        )

        logger.info(f"Enhanced bulk creation completed: {result['created']} created, {result['errors']} errors")
        return result

    except ValidationError as e:
        logger.warning(f"Enhanced bulk creation validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bulk creation validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Enhanced bulk creation business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put(
    "/bulk/update-selective",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Selective Bulk Update Components",
    description="Update multiple components with different data for each component",
    responses={
        status.HTTP_200_OK: {
            "description": "Bulk update completed with results",
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid update data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("selective_bulk_update_components")
@monitor_api_performance("selective_bulk_update_components")
async def selective_bulk_update_components(
    updates: List[Dict[str, Any]],
    batch_size: int = Query(100, ge=1, le=500, description="Batch size for processing"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> Dict[str, Any]:
    """Selective bulk update components with different data for each.

    This endpoint allows updating multiple components where each component
    can have different update data. Each update dictionary should include
    an 'id' field and the fields to update.

    Args:
        updates: List of update dictionaries with 'id' and update fields
        batch_size: Number of updates to process in each batch
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        Dict[str, Any]: Update results with statistics and errors

    Raises:
        HTTPException: If bulk update fails
    """
    logger.info(f"Selective bulk update requested for {len(updates)} components")

    try:
        # Perform selective bulk update
        result = component_service.bulk_update_selective(
            updates=updates,
            batch_size=batch_size
        )

        logger.info(f"Selective bulk update completed: {result['updated']} updated, {result['errors']} errors")
        return result

    except ValidationError as e:
        logger.warning(f"Selective bulk update validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bulk update validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Selective bulk update business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete(
    "/bulk/delete",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Bulk Delete Components",
    description="Delete multiple components with soft or hard delete options",
    responses={
        status.HTTP_200_OK: {
            "description": "Bulk deletion completed with results",
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid deletion parameters",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("bulk_delete_components")
@monitor_api_performance("bulk_delete_components")
async def bulk_delete_components(
    component_ids: List[int],
    soft_delete: bool = Query(True, description="Perform soft delete (default) or hard delete"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> Dict[str, Any]:
    """Bulk delete components with soft or hard delete options.

    This endpoint allows deleting multiple components at once with options for:
    - Soft delete (marks as deleted, preserves data)
    - Hard delete (permanently removes from database)

    Args:
        component_ids: List of component IDs to delete
        soft_delete: Whether to perform soft delete (default) or hard delete
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        Dict[str, Any]: Deletion results with statistics

    Raises:
        HTTPException: If bulk deletion fails
    """
    logger.info(f"Bulk delete requested for {len(component_ids)} components (soft={soft_delete})")

    try:
        # Perform bulk deletion
        result = component_service.bulk_delete_components(
            component_ids=component_ids,
            soft_delete=soft_delete,
            deleted_by_user_id=current_user.get('id')
        )

        logger.info(f"Bulk deletion completed: {result['deleted']} deleted, {result['not_found']} not found")
        return result

    except ValidationError as e:
        logger.warning(f"Bulk deletion validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bulk deletion validation failed: {str(e)}"
        )
    except BusinessLogicError as e:
        logger.warning(f"Bulk deletion business logic error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


# ============================================================================
# PERFORMANCE OPTIMIZATION ENDPOINTS
# ============================================================================

@router.get(
    "/performance/metrics",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get Performance Metrics",
    description="Get comprehensive performance metrics for the component system",
    responses={
        status.HTTP_200_OK: {
            "description": "Performance metrics retrieved successfully",
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_performance_metrics")
@monitor_api_performance("get_performance_metrics")
async def get_performance_metrics(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> Dict[str, Any]:
    """Get comprehensive performance metrics for the component system.

    This endpoint provides detailed performance metrics including:
    - Component statistics and counts
    - Cache performance metrics
    - Database query performance
    - System health indicators

    Args:
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        Dict[str, Any]: Performance metrics and statistics

    Raises:
        HTTPException: If metrics retrieval fails
    """
    logger.info("Performance metrics requested")

    try:
        # Get performance metrics
        metrics = component_service.get_performance_metrics()

        logger.info("Performance metrics retrieved successfully")
        return metrics

    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get performance metrics"
        )


@router.post(
    "/performance/optimize",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Optimize System Performance",
    description="Perform system performance optimization tasks",
    responses={
        status.HTTP_200_OK: {
            "description": "Performance optimization completed",
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("optimize_system_performance")
@monitor_api_performance("optimize_system_performance")
async def optimize_system_performance(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> Dict[str, Any]:
    """Perform system performance optimization tasks.

    This endpoint performs various optimization tasks including:
    - Cache warming for popular searches
    - Cache cleanup and maintenance
    - Database index analysis
    - Query optimization recommendations

    Args:
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        Dict[str, Any]: Optimization results and recommendations

    Raises:
        HTTPException: If optimization fails
    """
    logger.info("System performance optimization requested")

    try:
        # Perform optimization
        optimization_results = component_service.optimize_system_performance()

        logger.info("System performance optimization completed")
        return optimization_results

    except Exception as e:
        logger.error(f"Error in system optimization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="System optimization failed"
        )


@router.delete(
    "/cache/invalidate",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Invalidate Component Cache",
    description="Invalidate component cache entries for better performance",
    responses={
        status.HTTP_200_OK: {
            "description": "Cache invalidation completed",
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("invalidate_component_cache")
@monitor_api_performance("invalidate_component_cache")
async def invalidate_component_cache(
    component_id: Optional[int] = Query(None, description="Specific component ID to invalidate"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    component_service: ComponentService = Depends(get_component_service),
) -> Dict[str, Any]:
    """Invalidate component cache entries.

    This endpoint allows invalidating cache entries for:
    - Specific component (if component_id provided)
    - All component-related cache (if no component_id)

    Args:
        component_id: Specific component ID to invalidate (optional)
        current_user: Current authenticated user
        component_service: Component service dependency

    Returns:
        Dict[str, Any]: Cache invalidation results

    Raises:
        HTTPException: If cache invalidation fails
    """
    logger.info(f"Cache invalidation requested for component ID: {component_id or 'all'}")

    try:
        # Invalidate cache
        result = component_service.invalidate_component_cache(component_id)

        logger.info("Cache invalidation completed")
        return result

    except Exception as e:
        logger.error(f"Error invalidating cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Cache invalidation failed"
        )


# Export router
__all__ = ["router"]