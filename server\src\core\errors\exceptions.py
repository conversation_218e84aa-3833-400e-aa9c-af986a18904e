# src/core/errors/exceptions.py
"""Custom Exception Classes for Ultimate Electrical Designer.

This module defines custom exception classes used throughout the Ultimate Electrical
Designer backend application. It provides a hierarchy of exceptions for different
error types including validation errors, calculation errors, database errors,
and business logic errors.

All exceptions inherit from BaseApplicationException and include structured error
codes, messages, and context information for proper error handling and logging.
"""

from typing import Any, Dict


class BaseApplicationException(Exception):
    """Base exception for all application-specific errors."""

    def __init__(
        self,
        code: str,
        detail: str,
        category: str = "ServerError",
        status_code: int = 500,
        metadata: dict[str, Any] | None = None,
    ):
        super().__init__(detail)
        self.code = code
        self.detail = detail
        self.category = category
        self.status_code = status_code  # Useful for API/HTTP context, even in desktop
        self.metadata = metadata or {}


# Domain-specific exceptions
class NotFoundError(BaseApplicationException):
    """Exception raised when a requested resource is not found.

    This is a base class for all not-found related errors in the application.
    """

    pass  # Inherits attributes


class ProjectNotFoundError(NotFoundError):
    """Exception raised when a project with the specified ID is not found."""

    def __init__(self, project_id: str):
        super().__init__(
            code="PROJECT_NOT_FOUND",
            detail=f"Project with ID '{project_id}' not found.",
            category="ClientError",
            status_code=404,
            metadata={"project_id": project_id},
        )


class DataValidationError(BaseApplicationException):
    """Exception raised when data validation fails.

    This exception is typically used for Pydantic validation errors
    and other data validation failures.
    """

    def __init__(self, details: Dict[str, Any]):  # For Pydantic validation errors
        super().__init__(
            code="DATA_VALIDATION_FAILED",
            detail="Input validation failed.",
            category="Validation",
            status_code=422,  # Use 422 for validation errors (Unprocessable Entity)
            metadata={"validation_errors": details},
        )


class InvalidInputError(BaseApplicationException):
    """Exception raised for invalid user inputs."""

    def __init__(self, message: str):
        super().__init__(
            code="INVALID_INPUT",
            detail=message,
            category="Validation",
            status_code=400,
            metadata={"input_error": message},
        )


class ServiceError(BaseApplicationException):
    """Exception raised for service layer errors."""

    def __init__(self, message: str):
        super().__init__(
            code="SERVICE_ERROR",
            detail=message,
            category="ServiceError",
            status_code=500,
            metadata={"service_error": message},
        )


class DuplicateEntryError(BaseApplicationException):
    """Exception raised when a duplicate entry is detected."""

    def __init__(self, message: str, original_exception: Exception | None = None):
        super().__init__(
            code="DUPLICATE_ENTRY",
            detail=message,
            category="ClientError",
            status_code=409,
            metadata={
                "original_exception": (
                    str(original_exception) if original_exception else None
                )
            },
        )
        self.original_exception = original_exception


class DatabaseError(BaseApplicationException):
    """Exception raised for database operation errors."""

    def __init__(self, reason: str, original_exception: Exception | None = None):
        super().__init__(
            code="DB_OPERATION_FAILED",
            detail=f"Database error: {reason}",
            category="DatabaseError",
            status_code=500,
            metadata={
                "reason": reason,
                "original_exception": (
                    str(original_exception) if original_exception else None
                ),
            },
        )
        self.original_exception = original_exception


class ComponentNotFoundError(NotFoundError):
    """Exception raised when a component is not found."""

    def __init__(self, component_id_or_name: str):
        super().__init__(
            code="COMPONENT_NOT_FOUND",
            detail=f"Component '{component_id_or_name}' not found.",
            category="ClientError",
            status_code=404,
            metadata={"component_identifier": component_id_or_name},
        )


class CalculationError(BaseApplicationException):
    """Raised when a calculation cannot be performed or yields invalid results."""

    def __init__(self, details: str):
        super().__init__(
            code="CALCULATION_ERROR",
            detail=f"Calculation error: {details}.",
            category="Calculation",
            status_code=422,  # Unprocessable Entity
            metadata={"reason": details},
        )


class StandardComplianceError(CalculationError):
    """Raised when a design or calculation violates a specific engineering standard."""

    # Inherit from CalculationError or BaseApplicationException
    def __init__(self, details: str):
        super().__init__(details)


class BusinessLogicError(BaseApplicationException):
    """Exception for business logic violations."""

    def __init__(
        self,
        code: str = "BUSINESS_LOGIC_ERROR",
        detail: str | None = None,
        message: str | None = None,
    ):
        # Support both old (message) and new (code, detail) interfaces
        if detail is None and message is not None:
            detail = message
        elif detail is None:
            detail = "Business logic error"

        super().__init__(
            code=code,
            detail=detail,
            category="BusinessLogic",
            status_code=422,
            metadata={"business_logic_error": detail},
        )


class UtilityError(BaseApplicationException):
    """Exception for utility operation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "UTILITY_ERROR",
        context: dict[str, Any] | None = None,
    ):
        super().__init__(
            code=error_code,
            detail=message,
            category="UtilityError",
            status_code=500,
            metadata=context or {},
        )


class MiddlewareError(BaseApplicationException):
    """Exception for middleware operation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "MIDDLEWARE_ERROR",
        context: dict[str, Any] | None = None,
    ):
        super().__init__(
            code=error_code,
            detail=message,
            category="MiddlewareError",
            status_code=500,
            metadata=context or {},
        )


class APIError(BaseApplicationException):
    """Exception for API operation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "API_ERROR",
        context: dict[str, Any] | None = None,
        status_code: int = 500,
    ):
        super().__init__(
            code=error_code,
            detail=message,
            category="APIError",
            status_code=status_code,
            metadata=context or {},
        )


class SecurityError(BaseApplicationException):
    """Exception for security-related errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "SECURITY_ERROR",
        context: dict[str, Any] | None = None,
    ):
        super().__init__(
            code=error_code,
            detail=message,
            category="SecurityError",
            status_code=403,
            metadata=context or {},
        )


class ValidationError(BaseApplicationException):
    """Exception for validation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "VALIDATION_ERROR",
        context: dict[str, Any] | None = None,
    ):
        super().__init__(
            code=error_code,
            detail=message,
            category="ValidationError",
            status_code=400,
            metadata=context or {},
        )
