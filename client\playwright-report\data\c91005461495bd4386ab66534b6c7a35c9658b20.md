# Page snapshot

```yaml
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- button "Collapse issues badge":
  - img
- navigation:
  - button "previous" [disabled]:
    - img "previous"
  - text: 1/1
  - button "next" [disabled]:
    - img "next"
- img
- link "Next.js 15.3.0 (stale) Webpack":
  - /url: https://nextjs.org/docs/messages/version-staleness
  - img
  - text: Next.js 15.3.0 (stale) Webpack
- img
- dialog "Runtime Error":
  - text: Runtime Error
  - button "Copy Stack Trace":
    - img
  - button "No related documentation found" [disabled]:
    - img
  - link "Learn more about enabling Node.js inspector for server code with Chrome DevTools":
    - /url: https://nextjs.org/docs/app/building-your-application/configuring/debugging#server-side-code
    - img
  - paragraph: "Error: Cannot read properties of undefined (reading 'toLowerCase')"
  - paragraph:
    - img
    - text: src\app\dashboard\page.tsx (25:72) @ DashboardContent
    - button "Open in editor":
      - img
  - text: "23 | </h1> 24 | <p className=\"text-gray-600 mb-6\"> > 25 | Hello {user?.name}! You&apos;re logged in as a {user?.role.toLowerCase()}. | ^ 26 | </p> 27 | 28 | {/* Quick Actions */}"
  - paragraph: Call Stack 1
  - text: DashboardContent
  - button:
    - img
  - text: src\app\dashboard\page.tsx (25:72)
- contentinfo:
  - region "Error feedback":
    - paragraph:
      - link "Was this helpful?":
        - /url: https://nextjs.org/telemetry#error-feedback
    - button "Mark as helpful"
    - button "Mark as not helpful"
- 'heading "Application error: a client-side exception has occurred while loading localhost (see the browser console for more information)." [level=2]'
```