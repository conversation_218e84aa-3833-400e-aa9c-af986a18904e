/**
 * TypeScript type definitions for the Ultimate Electrical Designer API
 * Generated based on backend Pydantic schemas
 */

// Base types
export interface BaseSchema {
  id?: number;
}

export interface TimestampMixin {
  created_at: string;
  updated_at: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// User types
export enum UserRole {
  VIEWER = 'VIEWER',
  EDITOR = 'EDITOR',
  ADMIN = 'ADMIN'
}

export interface UserBase {
  name: string;
  email: string;
  role: UserRole;
  is_active: boolean;
}

export interface UserCreate extends UserBase {
  password: string;
}

export interface UserUpdate extends Partial<UserBase> {
  password?: string;
}

export interface UserRead extends UserBase, TimestampMixin {
  id: number;
  is_admin: boolean;
  last_login?: string;
}

export interface UserSummary {
  id: number;
  name: string;
  email: string;
  role: UserRole;
  is_active: boolean;
}

export type UserPaginatedResponse = PaginatedResponse<UserRead>;

// Authentication types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: UserRead;
}

export interface LogoutResponse {
  message: string;
  logged_out_at: string;
}

export interface PasswordChangeRequest {
  current_password: string;
  new_password: string;
}

export interface PasswordChangeResponse {
  message: string;
  changed_at: string;
}

// Error types
export interface ErrorResponse {
  detail: string;
  error_code?: string;
  timestamp?: string;
}

// API Response wrapper
export interface ApiResponse<T = any> {
  data?: T;
  error?: ErrorResponse;
  status: number;
}

// Query parameters for list endpoints
export interface ListQueryParams {
  skip?: number;
  limit?: number;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// Health check types
export interface HealthCheck {
  status: string;
  timestamp: string;
  version: string;
}

// API endpoints configuration
export interface ApiEndpoints {
  auth: {
    login: string;
    logout: string;
    token: string;
    changePassword: string;
  };
  users: {
    list: string;
    create: string;
    read: string;
    update: string;
    delete: string;
    me: string;
    summary: string;
  };
  health: {
    check: string;
  };
}

// API client configuration
export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// Request options
export interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  signal?: AbortSignal;
}

// Authentication context
export interface AuthContext {
  user: UserRead | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  token: string | null;
}

// Admin-specific types
export interface AdminStats {
  total_users: number;
  active_users: number;
  inactive_users: number;
  admin_users: number;
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormErrors {
  [key: string]: string | undefined;
}

// React Query keys
export const QueryKeys = {
  users: ['users'] as const,
  usersList: (params?: ListQueryParams) => ['users', 'list', params] as const,
  user: (id: number) => ['users', id] as const,
  currentUser: ['users', 'me'] as const,
  usersSummary: ['users', 'summary'] as const,
  health: ['health'] as const,
} as const;

// Mutation keys
export const MutationKeys = {
  login: ['auth', 'login'] as const,
  logout: ['auth', 'logout'] as const,
  changePassword: ['auth', 'changePassword'] as const,
  createUser: ['users', 'create'] as const,
  updateUser: ['users', 'update'] as const,
  deleteUser: ['users', 'delete'] as const,
  updateProfile: ['users', 'updateProfile'] as const,
} as const;
